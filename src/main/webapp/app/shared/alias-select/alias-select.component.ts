import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, OnInit, AfterViewInit } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { I<PERSON>lias } from 'app/entities/alias/alias.model';
import { AliasService } from 'app/entities/alias/service/alias.service';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'alias-select',
  templateUrl: './alias-select.component.html',
  styleUrls: ['./alias-select.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, TranslateModule],
})
export class AliasSelectComponent implements OnInit, AfterViewInit {
  @Input() formGroup!: FormGroup;
  @Input() controlName!: string;
  @Input() labelI18nKey = '';
  @Input() required = true;
  @Input() availableAliases: IAlias[] = [];
  @Input() loadAliasesInternally = true; // Nueva propiedad para controlar si se cargan los alias internamente

  @Output() aliasSelected = new EventEmitter<string>();

  isLoading = false;

  constructor(
    private translateService: TranslateService,
    private aliasService: AliasService
  ) {}

  ngOnInit(): void {
    // Cargar los alias si está configurado para hacerlo internamente y no se han proporcionado
    if (this.loadAliasesInternally && this.availableAliases.length === 0) {
      this.loadAliases();
    }
  }

  ngAfterViewInit(): void {
    // Verificar que el control existe en el formulario padre después de que la vista esté inicializada
    setTimeout(() => {
      const control = this.formGroup.get(this.controlName);
      if (!control) {
        console.error(`Control '${this.controlName}' no encontrado en el FormGroup`);
      }
    }, 0);
  }

  loadAliases(): void {
    this.isLoading = true;
    this.aliasService.query()
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: (response) => {
          this.availableAliases = response.body || [];
          console.log(`AliasSelectComponent (${this.controlName}): Loaded ${this.availableAliases.length} aliases`);
        },
        error: (error) => {
          console.error(`AliasSelectComponent (${this.controlName}): Error loading aliases:`, error);
        }
      });
  }

  onAliasSelected(event: Event): void {
    const select = event.target as HTMLSelectElement;
    this.aliasSelected.emit(select.value);
  }

  get isInvalid(): boolean {
    if (!this.controlName || !this.formGroup) return false;
    const control = this.formGroup.get(this.controlName);
    return !!control && control.invalid && (control.dirty || control.touched);
  }

  get errorMessage(): string {
    if (!this.controlName || !this.formGroup) return '';
    const control = this.formGroup.get(this.controlName);
    if (control?.errors?.['required'] && control.touched) {
      return this.translateService.instant('entity.validation.required');
    }
    return '';
  }

  // Helper method to safely get alias value
  getAliasValue(alias: IAlias): string {
    return alias.value || '';
  }
}
