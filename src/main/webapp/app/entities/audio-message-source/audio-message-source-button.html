@if (account) {
<button
  id="audio-message-source-create-button"
  class="btn btn-audio me-2"
  style="background-color: #ff6b35; border-color: #ff6b35; color: white"
  (click)="openModal()"
  data-cy="audioMessageSourceUploadButton"
  aria-label="Cargar archivo de audio"
>
  <fa-icon [icon]="faAudio" class="me-2"></fa-icon>
  <span jhiTranslate="forconversationsApp.audioMessageSource.upload.title">Load Audio</span>
</button>
}

<ng-template #content let-modal>
  <div class="modal-header">
    <h4 class="modal-title"><span jhiTranslate="forconversationsApp.audioMessageSource.upload.title">Upload Audio Message Source</span></h4>
    <button type="button" class="btn-close" (click)="close()" aria-label="Cerrar"></button>
  </div>

  <div class="modal-body">
    <form [formGroup]="uploadForm" (ngSubmit)="onSave()" class="d-flex flex-column h-100">
      
      <!-- Sección de subida de archivo - Solo se muestra si no se ha subido nada -->
      <div *ngIf="!audioMessageSourceResponseDto && !isUploading" class="text-center mb-4 p-4 border rounded" style="background-color: #f8f9fa">
        <input #fileInput type="file" class="d-none" accept="audio/*" (change)="onFileChange($event)" />
        <div class="mb-3">
          <fa-icon [icon]="faUpload" size="3x" class="text-primary mb-3"></fa-icon>
          <h5 jhiTranslate="forconversationsApp.audioMessageSource.upload.selectFile">Seleccionar Archivo de Audio</h5>
          <p class="text-muted" jhiTranslate="forconversationsApp.audioMessageSource.upload.selectFileDescription">
            Selecciona un archivo de audio para transcribir y crear un mensaje
          </p>
        </div>
        <button type="button" class="btn btn-primary btn-lg" (click)="triggerFileInput()">
          <fa-icon [icon]="faUpload" class="me-2"></fa-icon>
          <span jhiTranslate="forconversationsApp.audioMessageSource.upload.selectFile">Seleccionar Archivo</span>
        </button>
      </div>

      <!-- Estado de carga -->
      <div *ngIf="isUploading" class="text-center mb-4 p-4 border rounded" style="background-color: #f8f9fa">
        <div class="spinner-border text-primary mb-3" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <h5 jhiTranslate="forconversationsApp.audioMessageSource.upload.processing">Procesando audio...</h5>
        <p class="text-muted">Transcribiendo el contenido, por favor espera...</p>
      </div>

      <!-- Campos de configuración - Solo se muestran después de la transcripción -->
      <div *ngIf="uploadForm && audioMessageSourceResponseDto && !isUploading" class="mb-4">
        <div class="alert alert-success mb-3">
          <fa-icon [icon]="faCheckCircle" class="me-2"></fa-icon>
          <strong>Audio transcrito correctamente.</strong> Ahora completa los campos para guardar el mensaje.
        </div>
        
        <div class="row g-3">
          <!-- Time -->
          <div class="col-md-4">
            <label for="time" class="form-label">
              <span jhiTranslate="forconversationsApp.audioMessageSource.time">Fecha y Hora</span>
              <span class="text-danger">*</span>
            </label>
            <input
              type="datetime-local"
              id="time"
              class="form-control"
              name="time"
              formControlName="time"
              required
              [class.is-invalid]="uploadForm.get('time')?.invalid && uploadForm.get('time')?.touched"
            />
            <div class="invalid-feedback" *ngIf="uploadForm.get('time')?.invalid && uploadForm.get('time')?.touched">
              <span jhiTranslate="entity.validation.required">Este campo es requerido.</span>
            </div>
          </div>

          <!-- Sender Alias -->
          <div class="col-md-4">
            <alias-select
              [formGroup]="uploadForm"
              controlName="aliasSender"
              labelI18nKey="audioMessageSource.alias.sender"
              [selectedAliasId]="uploadForm.get('aliasSender')?.value"
              (aliasSelected)="onAliasSelected($event, 'sender')"
              [required]="true"
            ></alias-select>
          </div>

          <!-- Receiver Alias -->
          <div class="col-md-4">
            <alias-select
              [formGroup]="uploadForm"
              controlName="aliasReceiver"
              labelI18nKey="audioMessageSource.alias.receiver"
              [selectedAliasId]="uploadForm.get('aliasReceiver')?.value"
              (aliasSelected)="onAliasSelected($event, 'receiver')"
              [required]="true"
            ></alias-select>
          </div>
        </div>
      </div>

      <!-- Audio Preview - Solo se muestra después de la transcripción -->
      <div *ngIf="audioMessageSourceResponseDto && !isUploading" class="mb-4">
        <h5><span jhiTranslate="forconversationsApp.audioMessageSource.upload.preview">Vista Previa del Audio</span></h5>
        
        <!-- Reproductor de audio -->
        <div class="d-flex align-items-center mb-3">
          <button type="button" class="btn btn-outline-primary me-3" (click)="togglePlayPause()" [disabled]="!audioUrl">
            <fa-icon [icon]="isPlaying ? faPause : faPlay"></fa-icon>
            <span *ngIf="!isPlaying" class="ms-1" jhiTranslate="forconversationsApp.audioMessageSource.upload.play">Reproducir</span>
            <span *ngIf="isPlaying" class="ms-1" jhiTranslate="forconversationsApp.audioMessageSource.upload.pause">Pausar</span>
          </button>
          <audio #audioPlayer (ended)="onAudioEnd()" class="flex-grow-1" style="max-width: 100%">
            <source [src]="audioUrl" [type]="selectedFile?.type || 'audio/*'" />
            <span jhiTranslate="forconversationsApp.audioMessageSource.upload.browserNotSupported"
              >Tu navegador no soporta la reproducción de audio.</span
            >
          </audio>
        </div>

        <!-- Transcripción -->
        <div class="mt-3">
          <h6 class="d-flex align-items-center">
            <fa-icon [icon]="faMicrophone" class="me-2"></fa-icon>
            <span jhiTranslate="forconversationsApp.audioMessageSource.upload.transcription">Transcripción:</span>
          </h6>
          <div class="p-3 bg-light rounded" style="min-height: 80px; white-space: pre-wrap;">
            <span *ngIf="audioMessageSourceResponseDto?.text" class="text-dark">{{ audioMessageSourceResponseDto.text }}</span>
            <span *ngIf="!audioMessageSourceResponseDto?.text" class="text-muted font-italic">No se pudo transcribir el audio</span>
          </div>
        </div>
      </div>

      <!-- Error Alert -->
      <div *ngIf="showError" class="alert alert-danger mt-3">
        <fa-icon [icon]="faExclamationTriangle" class="me-2"></fa-icon>
        {{ errorMessage }}
        <button type="button" class="btn-close" (click)="closeAlert()" aria-label="Cerrar"></button>
      </div>

      <!-- Success Message -->
      <div *ngIf="showSuccess && !isSaving" class="alert alert-success mt-3">
        <fa-icon [icon]="faCheckCircle" class="me-2"></fa-icon>
        <span jhiTranslate="forconversationsApp.audioMessageSource.upload.success">Mensaje de audio guardado exitosamente.</span>
        <button type="button" class="btn-close" (click)="closeAlert()" aria-label="Cerrar"></button>
      </div>

      <!-- Form Actions -->
      <div class="modal-footer mt-auto" *ngIf="audioMessageSourceResponseDto && !isUploading">
        <button type="button" class="btn btn-outline-secondary" (click)="close()" [disabled]="isSaving">
          <fa-icon [icon]="faTimes" class="me-1"></fa-icon>
          <span jhiTranslate="forconversationsApp.audioMessageSource.upload.cancel">Cancelar</span>
        </button>
        <button type="submit" class="btn btn-primary" [disabled]="isSaving || !isFormValid()">
          <span *ngIf="!isSaving">
            <fa-icon [icon]="faSave" class="me-1"></fa-icon>
            <span jhiTranslate="forconversationsApp.audioMessageSource.upload.save">Guardar</span>
          </span>
          <span *ngIf="isSaving">
            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            <span jhiTranslate="forconversationsApp.audioMessageSource.upload.saving">Guardando...</span>
          </span>
        </button>
      </div>
    </form>
  </div>
</ng-template>
