import { CommonModule } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import {
  faCheckCircle,
  faExclamationTriangle,
  faMicrophone,
  faPause,
  faPlay,
  faSave,
  faTimes,
  faUpload,
} from '@fortawesome/free-solid-svg-icons';
import { NgbActiveModal, NgbModal, NgbModalRef, NgbTypeaheadModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import { AliasService } from 'app/entities/alias/service/alias.service';
import { IAlias } from 'app/entities/alias/alias.model';

import { AudioMessageSourceResponseDto } from 'app/entities/audio-message-source/model/audio-message-source-response-dto.model';
import { AudioMessageSourceService } from 'app/entities/audio-message-source/service/audio-message-source.service';
import { AliasSelectComponent } from 'app/shared/alias-select/alias-select.component';
import { AccountService } from 'app/core/auth/account.service';
import { Account } from 'app/core/auth/account.model';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'audio-message-source-button',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, FaIconComponent, NgbTypeaheadModule, TranslateModule, AliasSelectComponent],
  providers: [{ provide: AliasService, useClass: AliasService }, NgbActiveModal],
  templateUrl: './audio-message-source-button.html',
})
export class AudioMessageSourceButtonComponent implements OnInit {
  @ViewChild('fileInput') fileInput!: ElementRef;
  @ViewChild('audioPlayer') audioPlayer!: ElementRef<HTMLAudioElement>;
  @ViewChild('content') modalContent!: ElementRef;

  account: Account | null = null;
  faAudio = faMicrophone;
  uploadForm!: FormGroup;
  selectedFile: File | null = null;
  isUploading = false;
  isSaving = false;
  isPlaying = false;
  audioUrl: string | null = null;
  audioMessageSourceResponseDto: AudioMessageSourceResponseDto | null = null;
  availableAliases: IAlias[] = [];
  isLoadingAliases = false;

  // Icons
  faUpload = faUpload;
  faTimes = faTimes;
  faPlay = faPlay;
  faPause = faPause;
  faSave = faSave;
  faMicrophone = faMicrophone;
  faCheckCircle = faCheckCircle;
  faExclamationTriangle = faExclamationTriangle;
  // Alerts
  showSuccess = false;
  showError = false;
  errorMessage = '';

  // Available aliases
  private modalRef: NgbModalRef | undefined;

  constructor(
    private modalService: NgbModal,
    private fb: FormBuilder,
    public activeModal: NgbActiveModal,
    private aliasService: AliasService,
    private audioMessageSourceService: AudioMessageSourceService,
    private accountService: AccountService,
  ) {}

  ngOnInit(): void {
    this.accountService.identity().subscribe(account => (this.account = account));
    this.initializeForm();
    // Ya no necesitamos cargar los alias aquí, cada componente alias-select lo hará internamente
    // this.loadAliases();
  }

  loadAliases(): void {
    this.isLoadingAliases = true;
    this.aliasService.query()
      .pipe(
        finalize(() => {
          this.isLoadingAliases = false;
        })
      )
      .subscribe({
        next: (response) => {
          this.availableAliases = response.body || [];
          console.log('Aliases loaded:', this.availableAliases.length);
        },
        error: (error) => {
          console.error('Error loading aliases:', error);
          this.showError = true;
          this.errorMessage = 'Error al cargar los alias';
        }
      });
  }

  openModal(): void {
    try {
      // Asegurarse de que el formulario esté inicializado
      if (!this.uploadForm) {
        this.initializeForm();
      }
      
      // Ya no necesitamos verificar si los alias están cargados
      // if (this.availableAliases.length === 0 && !this.isLoadingAliases) {
      //   this.loadAliases();
      // }
      
      this.modalRef = this.modalService.open(this.modalContent, { size: 'lg', backdrop: 'static' });
      this.modalRef.closed.subscribe(() => this.resetState());
    } catch (error) {
      console.error('Error al abrir el modal:', error);
      this.showError = true;
      this.errorMessage = 'Error al inicializar el formulario';
    }
  }

  private resetState(): void {
    this.uploadForm.reset();
    this.selectedFile = null;
    this.isUploading = false;
    this.isSaving = false;
    this.isPlaying = false;
    this.showSuccess = false;
    this.showError = false;
    this.errorMessage = '';
    this.audioUrl = null;
    this.audioMessageSourceResponseDto = null;

    // Limpiar URL del audio si existe
    if (this.audioUrl) {
      URL.revokeObjectURL(this.audioUrl);
    }
  }

  private initializeForm(): void {
    this.uploadForm = this.fb.group({
      time: ['', Validators.required],
      aliasSender: ['', Validators.required],
      aliasReceiver: ['', Validators.required],
    });
  }

  // Trigger file input click
  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  // Handle file selection
  onFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];

      // Limpiar URL anterior si existe
      if (this.audioUrl) {
        URL.revokeObjectURL(this.audioUrl);
      }

      // Crear nueva URL para el audio
      this.audioUrl = URL.createObjectURL(this.selectedFile);
      console.log('Audio URL creada:', this.audioUrl);

      // Subir el archivo automáticamente
      this.uploadAudio();
    }
  }

  // Upload audio file to the server
  uploadAudio(): void {
    if (!this.selectedFile) {
      this.showError = true;
      this.errorMessage = 'Por favor selecciona un archivo de audio primero';
      return;
    }

    this.isUploading = true;
    this.showSuccess = false; // Asegurar que no se muestre éxito todavía
    this.showError = false; // Limpiar errores previos

    const formData = new FormData();
    formData.append('file', this.selectedFile);

    this.audioMessageSourceService.uploadAudio(formData).subscribe({
      next: (response: AudioMessageSourceResponseDto) => {
        console.log('Respuesta del servidor:', response);
        this.audioMessageSourceResponseDto = response;
        this.isUploading = false;
        // NO mostrar success aquí - solo después de guardar exitosamente

        // Inicializar el formulario con los datos de la respuesta
        this.initializeFormWithResponse(response);
      },
      error: (error: any) => {
        console.error('Error al subir el audio:', error);
        this.isUploading = false;
        this.showError = true;
        this.errorMessage = error.message || 'Error al subir el archivo de audio';
      },
    });
  }

  // Inicializar formulario con datos de la respuesta del servidor
  private initializeFormWithResponse(response: AudioMessageSourceResponseDto): void {
    if (!this.uploadForm) {
      this.initializeForm();
    }

    // Convertir el tiempo si viene en la respuesta
    let timeValue = '';
    if (response.time) {
      // Si viene como string ISO, convertir a datetime-local format
      const date = new Date(response.time);
      if (!isNaN(date.getTime())) {
        // Formato requerido por datetime-local: YYYY-MM-DDTHH:mm
        timeValue = date.toISOString().slice(0, 16);
      }
    } else {
      // Si no viene tiempo, usar el tiempo actual
      const now = new Date();
      timeValue = now.toISOString().slice(0, 16);
    }

    this.uploadForm.patchValue({
      time: timeValue,
      aliasSender: response.aliasSender?.id || '',
      aliasReceiver: response.aliasReceiver?.id || '',
    });

    // Marcar los campos como pristine ya que vienen del servidor
    this.uploadForm.markAsPristine();
  }

  // Toggle play/pause for audio player
  togglePlayPause(): void {
    if (!this.audioPlayer?.nativeElement) return;

    if (this.isPlaying) {
      this.audioPlayer.nativeElement.pause();
    } else {
      this.audioPlayer.nativeElement.play().catch(error => {
        console.error('Error playing audio:', error);
        this.showError = true;
        this.errorMessage = 'Error al reproducir el audio: ' + error.message;
      });
    }
    this.isPlaying = !this.isPlaying;
  }

  // Handle audio end event
  onAudioEnd(): void {
    this.isPlaying = false;
  }

  // Save the audio message (called by onSave for template compatibility)
  save(): void {
    if (!this.audioMessageSourceResponseDto) {
      this.showError = true;
      this.errorMessage = 'Por favor sube un archivo de audio primero';
      return;
    }

    if (this.uploadForm.invalid) {
      this.showError = true;
      this.errorMessage = 'Por favor completa todos los campos requeridos';
      return;
    }

    this.isSaving = true;

    // Prepare the data to save
    const formValue = this.uploadForm.value;

    // Convertir el tiempo del formulario a Instant
    const timeInstant = formValue.time ? new Date(formValue.time).toISOString() : undefined;

    // Get aliases by ID from the service
    const senderAliasId = formValue.aliasSender;
    const receiverAliasId = formValue.aliasReceiver;

    // Create the audio data with alias IDs (the backend will resolve the full alias objects)
    const audioData: AudioMessageSourceResponseDto = {
      ...this.audioMessageSourceResponseDto,
      time: timeInstant,
      aliasSender: senderAliasId ? { id: senderAliasId } : null,
      aliasReceiver: receiverAliasId ? { id: receiverAliasId } : null,
    };

    // Call the service to save the audio message source
    this.audioMessageSourceService.createAudioMessageSource(audioData).subscribe({
      next: () => {
        this.isSaving = false;
        this.showSuccess = true;

        // Cerrar el modal después de un breve delay para mostrar el mensaje de éxito
        setTimeout(() => {
          this.activeModal.close('saved');
        }, 1500);
      },
      error: (error: any) => {
        console.error('Error al guardar el mensaje de audio:', error);
        this.isSaving = false;
        this.showError = true;
        this.errorMessage = error.message || 'Error al guardar el mensaje de audio';
      },
    });
  }

  // Close the modal (alias for onCancel to maintain template compatibility)
  close(): void {
    this.activeModal.dismiss('cancel');
  }

  // Handle cancel button click
  onCancel(): void {
    this.close();
  }

  // Alias for save to maintain template compatibility
  onSave(): void {
    this.save();
  }

  // Close alert messages
  closeAlert(): void {
    this.showError = false;
    this.showSuccess = false;
  }

  // Handle alias selection
  onAliasSelected(aliasId: string, type: 'sender' | 'receiver'): void {
    if (type === 'sender') {
      this.uploadForm.patchValue({
        aliasSender: aliasId,
      });
    } else {
      this.uploadForm.patchValue({
        aliasReceiver: aliasId,
      });
    }
    this.uploadForm.get(type === 'sender' ? 'aliasSender' : 'aliasReceiver')?.markAsTouched();
  }

  isFormValid(): boolean {
    if (!this.audioMessageSourceResponseDto) return false;

    const form = this.uploadForm;
    const hasTranscription = !!this.audioMessageSourceResponseDto?.text?.trim();
    const hasTime = !!form.get('time')?.value;
    const hasSender = !!form.get('aliasSender')?.value;
    const hasReceiver = !!form.get('aliasReceiver')?.value;

    return hasTranscription && hasTime && hasSender && hasReceiver && form.valid;
  }
}
