package com.wishforthecure.forconversations.service;

import java.io.IOException;

import com.wishforthecure.forconversations.service.dto.AudioMessageSourceResponseDto;

import reactor.core.publisher.Mono;

public interface AudioMessageSourceService {

    public Mono<AudioMessageSourceResponseDto> loadFile(byte[] audioBytes) throws IOException;

    public Mono<Void> save(AudioMessageSourceResponseDto audioMessageSourceResponseDto);
}
