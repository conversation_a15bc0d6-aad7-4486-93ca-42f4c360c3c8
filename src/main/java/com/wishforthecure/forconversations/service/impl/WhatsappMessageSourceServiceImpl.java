package com.wishforthecure.forconversations.service.impl;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import com.wishforthecure.forconversations.domain.WhatsappMessageSource;
import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.domain.enumeration.UploadStatus;
import com.wishforthecure.forconversations.repository.WhatsappMessageSourceRepository;
import com.wishforthecure.forconversations.service.MessageService;
import com.wishforthecure.forconversations.service.SourceService;
import com.wishforthecure.forconversations.service.WhatsappMessageSourceService;
import com.wishforthecure.forconversations.service.dto.AliasDTO;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import com.wishforthecure.forconversations.service.dto.WhatsAppConfirmUploadRequestDto;
import com.wishforthecure.forconversations.service.dto.WhatsAppInitiateUploadResponseDto;
import com.wishforthecure.forconversations.service.dto.WhatsAppUploadParseDTO;

import reactor.core.publisher.Mono;

@Service
public class WhatsappMessageSourceServiceImpl implements WhatsappMessageSourceService {

    private static final Logger LOG = LoggerFactory.getLogger(WhatsappMessageSourceServiceImpl.class);

    private static final Pattern WHATSAPP_SENDER_PATTERN = Pattern
            .compile("^\\d{1,2}/\\d{1,2}/\\d{2,4}, \\d{1,2}:\\d{2}\\s*-\\s*([^:]+):");
    private static final Pattern WHATSAPP_MESSAGE_PATTERN = Pattern.compile(
            "(\\d{1,2}/\\d{1,2}/\\d{2}),\\s*(\\d{1,2}:\\d{2})\\s*-\\s*([^:]+):\\s*(.*)");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("d/M/yy, H:m").withZone(
            ZoneId.systemDefault());

    private final MessageService messageService;
    private final SourceService sourceService;
    private final WhatsappMessageSourceRepository whatsappMessageSourceRepository;

    public WhatsappMessageSourceServiceImpl(
            MessageService messageService,
            SourceService sourceService,
            WhatsappMessageSourceRepository whatsappMessageSourceRepository) {
        this.messageService = messageService;
        this.sourceService = sourceService;
        this.whatsappMessageSourceRepository = whatsappMessageSourceRepository;
    }

    @Override
    public Mono<WhatsAppInitiateUploadResponseDto> initiateUpload(byte[] bytes) {
        String namePersonOne = null;
        String namePersonTwo = null;

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new ByteArrayInputStream(bytes), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                Matcher matcher = WHATSAPP_SENDER_PATTERN.matcher(line);
                if (matcher.find()) {
                    String currentSender = matcher.group(1).trim();
                    if (namePersonOne == null) {
                        namePersonOne = currentSender;
                    } else if (namePersonTwo == null && !namePersonOne.equals(currentSender)) {
                        namePersonTwo = currentSender;
                    }
                }
                if (namePersonOne != null && namePersonTwo != null) {
                    break; // Optimization: Stop parsing once both names are found
                }
            }
        } catch (Exception e) {
            LOG.error("Error during participant name extraction", e);
            return Mono.error(new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR,
                    "Failed to parse participant names."));
        }

        WhatsappMessageSource source = new WhatsappMessageSource();
        source.setUploadId(UUID.randomUUID());
        source.setStatus(UploadStatus.PENDING_CONFIRMATION);
        source.setCreatedAt(Instant.now());
        source.setFile(bytes);
        source.setFileContentType("text/plain");
        source.setNamePersonOne(namePersonOne);
        source.setNamePersonTwo(namePersonTwo);

        return whatsappMessageSourceRepository
                .save(source)
                .map(savedSource -> {
                    WhatsAppInitiateUploadResponseDto response = new WhatsAppInitiateUploadResponseDto();
                    response.setUploadId(savedSource.getUploadId());
                    response.setNamePersonOne(savedSource.getNamePersonOne());
                    response.setNamePersonTwo(savedSource.getNamePersonTwo());
                    return response;
                });
    }

    @Override
    public Mono<Void> confirmAndProcessUpload(WhatsAppConfirmUploadRequestDto confirmRequest) {
        return whatsappMessageSourceRepository
                .findByUploadId(confirmRequest.getUploadId())
                .switchIfEmpty(
                        Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND, "Upload process not found.")))
                .flatMap(provisionalSource -> {
                    if (provisionalSource.getStatus() != UploadStatus.PENDING_CONFIRMATION) {
                        return Mono.error(new ResponseStatusException(HttpStatus.BAD_REQUEST,
                                "Upload has already been processed."));
                    }

                    List<WhatsAppUploadParseDTO> parsedLines = parseChatContent(provisionalSource.getFile());

                    String namePersonOne = provisionalSource.getNamePersonOne();
                    AliasDTO aliasPersonOne = confirmRequest.getAliasPersonOne();
                    AliasDTO aliasPersonTwo = confirmRequest.getAliasPersonTwo();
                    String sender = "";
                    String recipients = "";

                    List<MessageDTO> messageDTOList = new ArrayList<>();
                    for (WhatsAppUploadParseDTO parsedLine : parsedLines) {
                        if (namePersonOne.equals(parsedLine.getSender())) {
                            sender = aliasPersonOne.getValue();
                            recipients = aliasPersonTwo != null ? aliasPersonTwo.getValue() : "";
                        } else {
                            sender = aliasPersonTwo != null ? aliasPersonTwo.getValue() : "";
                            recipients = aliasPersonOne.getValue();
                        }
                        MessageDTO messageDTO = new MessageDTO(parsedLine.getTime(), parsedLine.getContent(),
                                sender, recipients, SourceType.WHATSAPP);
                        messageDTOList.add(messageDTO);
                    }

                    return messageService
                            .saveAll(messageDTOList)
                            .flatMap(savedMessages -> {
                                List<String> messageIds = savedMessages.stream().map(MessageDTO::getId)
                                        .collect(Collectors.toList());

                                SourceDTO sourceDTO = new SourceDTO(Instant.now(), messageIds, SourceType.WHATSAPP, provisionalSource.getFile(), provisionalSource.getFileContentType());    

                                return sourceService.save(sourceDTO);
                            })
                            .then(
                                    Mono.defer(() -> {
                                        provisionalSource.setStatus(UploadStatus.PROCESSED);
                                        provisionalSource.setAliasPersonOne(aliasPersonOne.getValue());
                                        if (aliasPersonTwo != null) {
                                            provisionalSource.setAliasPersonTwo(aliasPersonTwo.getValue());
                                        }
                                        provisionalSource.setFile(null);
                                        // provisionalSource.setFileContentType(null); // Keep original content type
                                        return whatsappMessageSourceRepository.save(provisionalSource);
                                    }))
                            .then();
                });
    }

    private List<WhatsAppUploadParseDTO> parseChatContent(byte[] bytes) {
        List<WhatsAppUploadParseDTO> parsedLines = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new ByteArrayInputStream(bytes), StandardCharsets.UTF_8))) {
            String line;
            StringBuilder currentMessageContent = new StringBuilder();
            WhatsAppUploadParseDTO currentMessage = null;
            int lineNumber = 0;

            while ((line = reader.readLine()) != null) {
                Matcher matcher = WHATSAPP_MESSAGE_PATTERN.matcher(line);
                if (matcher.matches()) {
                    if (currentMessage != null) {
                        currentMessage.setContent(currentMessageContent.toString().trim());
                        parsedLines.add(currentMessage);
                    }

                    try {
                        LocalDateTime ldt = LocalDateTime.parse(matcher.group(1) + ", " + matcher.group(2),
                                DATE_TIME_FORMATTER);
                        Instant instant = ldt.atZone(ZoneId.systemDefault()).toInstant();
                        currentMessage = WhatsAppUploadParseDTO.of(instant, matcher.group(3), "", ++lineNumber);
                        currentMessageContent = new StringBuilder(matcher.group(4));
                    } catch (DateTimeParseException e) {
                        LOG.warn("Could not parse date-time for line, skipping: {}", line);
                        currentMessage = null;
                    }
                } else if (currentMessage != null) {
                    currentMessageContent.append("\n").append(line);
                }
            }
            if (currentMessage != null) {
                currentMessage.setContent(currentMessageContent.toString().trim());
                parsedLines.add(currentMessage);
            }
        } catch (Exception e) {
            LOG.error("Failed to parse full chat content", e);
            throw new RuntimeException("Failed to parse chat file", e);
        }
        return parsedLines;
    }
}
