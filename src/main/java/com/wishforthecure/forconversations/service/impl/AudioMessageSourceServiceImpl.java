package com.wishforthecure.forconversations.service.impl;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.tika.Tika;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.speech.v2.AutoDetectDecodingConfig;
import com.google.cloud.speech.v2.RecognitionConfig;
import com.google.cloud.speech.v2.RecognitionFeatures;
import com.google.cloud.speech.v2.RecognizeRequest;
import com.google.cloud.speech.v2.RecognizeResponse;
import com.google.cloud.speech.v2.SpeechClient;
import com.google.cloud.speech.v2.SpeechSettings;
import com.google.protobuf.ByteString;
import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.service.AudioMessageSourceService;
import com.wishforthecure.forconversations.service.MessageService;
import com.wishforthecure.forconversations.service.SourceService;
import com.wishforthecure.forconversations.service.dto.AudioMessageSourceResponseDto;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import com.wishforthecure.forconversations.service.dto.SourceDTO;

import reactor.core.publisher.Mono;

@Service
public class AudioMessageSourceServiceImpl implements AudioMessageSourceService {

    private static final Logger LOG = LoggerFactory.getLogger(AudioMessageSourceServiceImpl.class);

    @Value("${application.google.speech.language-code:es-ES}")
    private String languageCode;

    @Value("${application.google.speech.enabled:true}")
    private boolean enabled;

    @Value("${application.google.speech.credentials-path:}")
    private String credentialsPath;

    @Value("${application.google.speech.location:global}")
    private String location;

    @Value("${application.google.speech.project-id:}")
    private String projectId;

    @Value("${application.google.speech.sample-rate:16000}")
    private int sampleRate;

    private final MessageService messageService;
    private final SourceService sourceService;

    public AudioMessageSourceServiceImpl(MessageService messageService, SourceService sourceService) {
        this.messageService = messageService;
        this.sourceService = sourceService;
    }

    private static final Set<String> SUPPORTED_MIME_TYPES = new HashSet<>(
            Arrays.asList("audio/wav", "audio/x-wav", "audio/flac", "audio/mpeg", "audio/opus", "audio/ogg",
                    "audio/webm", "application/ogg"));

    @Override
    public Mono<AudioMessageSourceResponseDto> loadFile(byte[] audioBytes) {
        if (!enabled) {
            return Mono.error(new IllegalStateException("Transcription service is disabled."));
        }
        if (audioBytes == null || audioBytes.length == 0) {
            return Mono.error(new IllegalArgumentException("Audio byte array cannot be null or empty"));
        }

        Tika tika = new Tika();
        String mimeType = tika.detect(audioBytes);

        if (!SUPPORTED_MIME_TYPES.contains(mimeType)) {
            return Mono.error(new IllegalArgumentException("Unsupported audio format: " + mimeType));
        }
        LOG.info("Detected MIME Type: {}", mimeType);

        try {
            // String transcription = transcribeWithGoogleCloud(audioBytes);
            String transcription = "Esto es un texto de prueba loraasdfdasf";
            LOG.info("Transcription completed successfully.");

            AudioMessageSourceResponseDto audioMessageSourceResponseDto = new AudioMessageSourceResponseDto();
            audioMessageSourceResponseDto.setText(transcription);
            audioMessageSourceResponseDto.setTime(Instant.now());
            return Mono.just(audioMessageSourceResponseDto);
        } catch (Exception e) {
            LOG.error("Error during audio transcription", e);
            return Mono.error(new RuntimeException("Error transcribing audio", e));
        }
    }

    private String transcribeWithGoogleCloud(byte[] bytes) throws IOException {
        LOG.info("Transcribing audio with Google Cloud Speech V2...");

        try (SpeechClient speechClient = createSpeechClient()) {
            // Use configured project ID
            if (projectId == null || projectId.isBlank()) {
                throw new IllegalStateException("Project ID must be configured in application.yml");
            }

            String recognizerName = String.format("projects/%s/locations/%s/recognizers/_", projectId, location);
            ByteString audioBytes = ByteString.copyFrom(bytes);

            RecognitionFeatures features = RecognitionFeatures.newBuilder().setEnableAutomaticPunctuation(true).build();

            AutoDetectDecodingConfig autoDetectConfig = AutoDetectDecodingConfig.newBuilder().build();

            RecognitionConfig config = RecognitionConfig.newBuilder()
                    .setAutoDecodingConfig(autoDetectConfig)
                    .addLanguageCodes(languageCode)
                    .setModel("latest_long")
                    .setFeatures(features)
                    .build();

            RecognizeRequest request = RecognizeRequest.newBuilder()
                    .setRecognizer(recognizerName)
                    .setConfig(config)
                    .setContent(audioBytes)
                    .build();

            LOG.info("Sending transcription request to Google Speech API V2...");
            RecognizeResponse response = speechClient.recognize(request);

            String finalTranscription = response
                    .getResultsList()
                    .stream()
                    .map(result -> result.getAlternativesList().isEmpty() ? ""
                            : result.getAlternativesList().get(0).getTranscript())
                    .filter(transcript -> !transcript.isEmpty())
                    .collect(Collectors.joining(" "))
                    .trim();

            LOG.info("Transcription completed successfully. Length: {} characters", finalTranscription.length());
            return finalTranscription;
        } catch (Exception e) {
            LOG.error("Failed to transcribe with Google Cloud Speech V2", e);
            throw new RuntimeException("Error transcribing audio with Google Cloud Speech V2", e);
        }
    }

    private GoogleCredentials getCredentials() throws IOException {
        if (credentialsPath != null && !credentialsPath.trim().isEmpty()) {
            Path credentialsFile = Path.of(credentialsPath);
            if (Files.exists(credentialsFile)) {
                LOG.info("Loading Google Cloud credentials from: {}", credentialsPath);
                return GoogleCredentials.fromStream(Files.newInputStream(credentialsFile));
            } else {
                LOG.warn("Credentials file not found at: {}, falling back to default credentials", credentialsPath);
            }
        }
        LOG.info("Using default Google Cloud credentials (Application Default Credentials)");
        return GoogleCredentials.getApplicationDefault();
    }

    private SpeechClient createSpeechClient() throws IOException {
        try {
            GoogleCredentials credentials = getCredentials();
            SpeechSettings settings = SpeechSettings.newBuilder().setCredentialsProvider(() -> credentials).build();
            return SpeechClient.create(settings);
        } catch (IOException e) {
            LOG.error("Failed to create Google Cloud Speech V2 client", e);
            throw new IOException("Failed to create Google Cloud Speech V2 client", e);
        }
    }

    @Override
    public Mono<Void> save(AudioMessageSourceResponseDto audioMessageSourceResponseDto) {
        // Create MessageDto
        MessageDTO messageDTO = new MessageDTO(audioMessageSourceResponseDto.getTime(),
                audioMessageSourceResponseDto.getAliasSender().getValue(),
                audioMessageSourceResponseDto.getAliasReceiver().getValue(),
                audioMessageSourceResponseDto.getText(),
                SourceType.AUDIO);

        return messageService.save(messageDTO)
                .flatMap(savedMessage -> {
                    // Create SourceDto
                    SourceDTO sourceDTO = new SourceDTO(
                            audioMessageSourceResponseDto.getTime(),
                            List.of(savedMessage.getId()),
                            SourceType.AUDIO,
                            audioMessageSourceResponseDto.getFile(),
                            audioMessageSourceResponseDto.getFileContentType());

                    return sourceService.save(sourceDTO);
                })
                .then();
    }

}
