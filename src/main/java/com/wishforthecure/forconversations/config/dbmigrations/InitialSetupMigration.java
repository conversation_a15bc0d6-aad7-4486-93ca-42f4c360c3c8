package com.wishforthecure.forconversations.config.dbmigrations;

import java.time.Instant;
import java.util.TimeZone;
import java.util.UUID;

import org.springframework.data.mongodb.core.MongoTemplate;

import com.wishforthecure.forconversations.config.Constants;
import com.wishforthecure.forconversations.domain.Authority;
import com.wishforthecure.forconversations.domain.User;
import com.wishforthecure.forconversations.security.AuthoritiesConstants;

import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackExecution;

/**
 * Creates the initial database setup.
 */
@ChangeUnit(id = "users-initialization", order = "001")
public class InitialSetupMigration {

    private final MongoTemplate template;

    public InitialSetupMigration(MongoTemplate template) {
        this.template = template;
    }

    @Execution
    public void changeSet() {
        Authority userAuthority = createUserAuthority();
        userAuthority = template.save(userAuthority);
        Authority adminAuthority = createAdminAuthority();
        adminAuthority = template.save(adminAuthority);
        addUsers(userAuthority, adminAuthority);
    }

    @RollbackExecution
    public void rollback() {
    }

    private Authority createAuthority(String authority) {
        Authority adminAuthority = new Authority();
        adminAuthority.setName(authority);
        return adminAuthority;
    }

    private Authority createAdminAuthority() {
        Authority adminAuthority = createAuthority(AuthoritiesConstants.ADMIN);
        return adminAuthority;
    }

    private Authority createUserAuthority() {
        Authority userAuthority = createAuthority(AuthoritiesConstants.USER);
        return userAuthority;
    }

    private void addUsers(Authority userAuthority, Authority adminAuthority) {
        User admin = createAdmin(adminAuthority, userAuthority);
        template.save(admin);
    }

    private User createAdmin(Authority adminAuthority, Authority userAuthority) {
        User adminUser = new User();
        adminUser.setId(UUID.randomUUID().toString());
        adminUser.setLogin("admin");
        adminUser.setPassword("2a$10$9VhyuCYebOVy8QEf8X8riejFfddE476rnHyA8J3x8gS1W2BnTZY5m");
        adminUser.setFirstName("admin");
        adminUser.setLastName("Administrator");
        adminUser.setEmail("<EMAIL>");
        adminUser.setActivated(true);
        adminUser.setLangKey("en");
        adminUser.setTimeZone(TimeZone.getTimeZone("Europe/Madrid"));
        adminUser.setCreatedBy(Constants.SYSTEM);
        adminUser.setCreatedDate(Instant.now());
        adminUser.getAuthorities().add(adminAuthority);
        adminUser.getAuthorities().add(userAuthority);
        return adminUser;
    }
}
